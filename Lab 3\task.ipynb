{"cells": [{"cell_type": "code", "execution_count": null, "id": "4c0f4e5c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "file_path = \"data.xlsx\"\n", "try:\n", "    df = pd.read_excel(file_path)\n", "except Exception as e:\n", "    print(f\"في مشكلة في قراءة الملف: {e}\")\n", "    exit()\n", "\n", "print(\"📄 البيانات الموجودة في الملف:\")\n", "print(df)\n", "print(\"\\n✍️ من فضلك أدخل البيانات الجديدة:\")\n", "name = input(\"الاسم: \")\n", "age = input(\"السن: \")\n", "job = input(\"الوظيفة: \")\n", "\n", "new_row = {'اسم': name, 'السن': age, 'الوظيفة': job}\n", "df = df._append(new_row, ignore_index=True)\n", "\n", "output_file = file_path.replace(\".xlsx\", \"_updated.xlsx\")  # بيحفظه باسم جديد\n", "df.to_excel(output_file, index=False)\n", "\n", "print(f\"\\n✅ تم حفظ الملف في: {output_file}\")\n", "\n", "print(\"\\n📄 البيانات بعد التحديث:\")\n", "print(df)\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}