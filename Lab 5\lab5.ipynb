# Task
import pandas as pd
from sklearn.preprocessing import LabelEncoder

df = pd.read_csv(r'D:\Ahmed Rezk\NTI\ai\labs\Lab 5\titanic.csv')

print("Before Encoding:")
print(df[['Gender', 'Embarked']].head())

for column in ['Gender', 'Embarked']:
    df[column].fillna(df[column].mode()[0], inplace=True)

le_gender = LabelEncoder()
le_embarked = LabelEncoder()

df['Gender'] = le_gender.fit_transform(df['Gender'])
df['Embarked'] = le_embarked.fit_transform(df['Embarked'])

print("\nAfter Encoding:")
print(df[['Gender', 'Embarked']].head())


import pandas as pd
import numpy as np
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score

col_names = ['pregnant', 'glucose', 'bp', 'skin', 'insulin', 'bmi', 'pedigree', 'age', 'label']
df = pd.read_csv(r'D:\Ahmed Rezk\NTI\ai\labs\Lab 5\diabetes.csv', names=col_names)

features = ['pregnant', 'glucose', 'bp', 'skin', 'insulin', 'bmi', 'pedigree', 'age']
X = df[features]
y = df.label

x_train, x_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=1)