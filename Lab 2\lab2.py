import numpy as np

# v1 = np.array([10,20,30])
# v2 = np.array([[1],[2],[3]])
# print(v1)
# print(v2)

# # ============================================ Comment ============================================ #

# arr = np.array([[1,2,3],[4,5,6]])
# print(arr)

# # ============================================ Comment ============================================ #

# list1 = np.arange(start=1,stop = 13,step = 1)
# print ( "vector" )
# print(list1)

# ## Create a 2D numpy array using python Lists
# arr2 = np.array(list1.reshape(3, 4))
# print ( "matrix" )
# print(arr2)

# # ============================================ Comment ============================================ #

# # creating a 1-D list (Horizontal)
# list1 = [5, 6, 9]

# # creating a 2-D list (Vertical)
# list2 = [1, 2, 3]

# # creating a frist vector
# v1 = np.array(list1)
# print ("Frist Vector     : " + str(v1))

# # creating a second vector
# v2 = np.array(list2)
# print ("Second Vector    : " + str(v2))

# # adding
# addition = v1 + v2
# print ("Addition         : " + str(addition))

# # subtracting
# subtraction = v1 - v2
# print ("Subtraction      : " + str(subtraction))

# # multiplying
# multiplication = v1 * v2
# print ("Multiplication   : " + str(multiplication))

# # dividing
# division = v1 / v2
# print ("Division         : " + str(division))

# ============================================ Comment ============================================ #

# #Python Implementation of Ll norm
# from numpy import array
# from numpy. linalg import norm
# arr = array([1, 2, 3, 4, 5])
# print(arr)
# norm_l1 = norm(arr,1)
# print (norm_l1)

# ============================================ Comment ============================================ #

# #Python Implementation of L2 norm
# from numpy import array
# from numpy. linalg import norm
# arr = array([1, 2, 3, 4, 5])
# print(arr)
# norm_l2 = norm(arr,1)
# print (norm_l2)

# ============================================ Comment ============================================ #

# # Calculate matrix determinant
# # creating a 2X2 Numpy matrix
# n_array = np.array([[50,29],[30,44]])
# # Displaying the Matrix
# print("Numpy Matrix is:")
# print(n_array)
# # calculating the determinant of matrix
# det = np.linalg.det(n_array)

# print("\nDeterminant of given 2X2 matrix:" )
# print(int(det))

# ============================================ Comment ============================================ #

# # calcute matrix rank
# my_matrix = np.array([[1,2,1],[3,4,7],[3,6,3]]) # rank = 2
# print(my_matrix)
# rank = np.linalg.matrix_rank(my_matrix)
# print("Rank of the matrix is: ",rank)

# ============================================ Comment ============================================ #

# Calculate matrix inverse
A = np.array([[3, 7], [2, 5]])
print(A)
A_inv = np.linalg.inv(A)
print(A_inv)

# ============================================ Comment ============================================ #

# Calculate matrix transpose
A = np.array([[1, 2], [3, 4]])
print(A)
A_trans = np.transpose(A)
print(A_trans)

# ============================================ Comment ============================================ #

# Calculate matrix trace
