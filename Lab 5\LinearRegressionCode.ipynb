import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
import matplotlib.pyplot as plt

"""
The dataset consists of age, sex, BMI(body mass index), children, 
smoker and region feature, which are independent and charge as a dependent feature.
We will predict individual medical costs billed by health insurance.
"""
df = pd.read_csv(r'D:\Ahmed Rezk\NTI\ai\labs\Lab 5\insurance.csv')
print(df)

print('\nNumber of rows and columns in the data set: ',df.shape)

df.head()

df.describe()

# Check for missing value
missing_values = df.isnull().sum()
print(missing_values)  # This will show the count of missing values in each column

# ------ features and output (label) ------
input_df = df.drop(columns='expenses')
target_df = df.expenses

input_df

target_df

from sklearn.preprocessing import LabelEncoder

# Define the columns for encoding
columns_to_encode = ["sex", "smoker", "region"]  # Replace with your column names

# Create a LabelEncoder object
le = LabelEncoder()

# Loop through each column and encode
for col in columns_to_encode:
  encoded_data = le.fit_transform(input_df[col])
  input_df["encoded_" + col] = encoded_data

input_df

# Define the columns to drop
columns_to_drop = ["sex", "smoker", "region"]  # Replace with your column names

# Drop the columns
input_df = input_df.drop(columns_to_drop, axis=1)

input_df

from sklearn.preprocessing import MinMaxScaler

# Specify the column name for scaling
column_name = "expenses"  # Replace with the actual column name

# Create a MinMaxScaler object
scaler = MinMaxScaler()

# Fit the scaler on the column data (learn range)
scaler.fit(df[[column_name]])  # Double square brackets to select a DataFrame

# Transform the column data using the fitted scaler
scaled_data = scaler.transform(df[[column_name]])


target_df = scaled_data

target_df

from sklearn.model_selection import train_test_split

X_train, X_test, Y_train, Y_test = train_test_split(input_df, target_df, test_size=0.2, random_state=42)

#For linear regression, Y=the value we want to predict
#X= all independent variables upon which Y depends. 
#3 steps for linear regression....
#Step 1: Create the instance of the model
#Step 2: .fit() to train the model or fit a linear model
#Step 3: .predict() to predict Y for given X values. 

#object
reg = LinearRegression()
#fit
reg.fit(X_train, Y_train)

prediction_test = reg.predict(X_test)

from sklearn.metrics import mean_absolute_error 
from sklearn.metrics import mean_squared_error 
from sklearn.metrics import median_absolute_error
import matplotlib.pyplot as plt

prediction_test = reg.predict(X_test)    
print(Y_test, prediction_test)
#----------------------------------------------------
#Calculating Mean Absolute Error
MAEValue = mean_absolute_error(Y_test, prediction_test, multioutput='uniform_average') # it can be raw_values
print('Mean Absolute Error Value is : ', MAEValue)

#----------------------------------------------------
#Calculating Mean Squared Error
MSEValue = mean_squared_error(Y_test, prediction_test, multioutput='uniform_average') # it can be raw_values
print('Mean Squared Error Value is : ', MSEValue)

#----------------------------------------------------
#Calculating Median Squared Error
MdSEValue = median_absolute_error(Y_test, prediction_test)
print('Median Squared Error Value is : ', MdSEValue )