# import random
import numpy as np
from numpy import array
from numpy.linalg import norm
import pandas as pd
import matplotlib.pyplot as plt


# # this is a comment
# print("hello world ")
# x=5
# y='hello'

# firstName='kareem'
# lastName='ali'
# age=21
# location='sammanoud'
# print(x)
# print(y, firstName, lastName, age, location)


# number1=int(input('enter a first number : '))
# number2=int(input('enter a sound number : '))
# sum=number1+number2
# print('the sum is :', sum)

# name=str(input('enter your name : '))
# print('hello', name)

# fi,so,tr="orange", "apple", "banana"
# print(fi, so, tr)

# f=s=t="red"
# print(f, s, t)

# def myFunction():
#     print("This is a function")


# print(random.randrange(1,10))


# oneList=['kareem','ali','mohamed','sadek','mohamed','amer']
# print(oneList)
# print(len(oneList))
# oneList[1]=500
# print(oneList)
# # print(oneList[2:5])
# print(oneList[2:])
# print(oneList[-4:-1])
# if "amer" in oneList:
#     print('yes')
# oneList[1:3]=['reda','sadek']
# print(oneList)
# oneList.insert(2,'fathy')
# print(oneList)
# oneList.append('anter')
# print(oneList)
# oneList.remove('mohamed')
# print(oneList)
# oneList.pop(2)
# print(oneList)
# oneList.clear()

# twoList=[1,2,3,3,4,5,6,7,8,9]
# # oneList.extend(twoList)
# # print(oneList)

# for i in oneList:
#     print(i)

# twoList.sort(reverse=True)
# print(twoList)



# tuple1 = ("apple", "banana", "cherry")
# print(tuple1)

# x=("apple", "banana", "cherry")
# y=list(x)
# y[1]="kiwi"
# x=tuple(y)
# print(x)

# array1=np.array([11,22,33,44,55])
# array2 = np.array([[1, 2, 3, 4, 5],[6,4,87,9,9]])
# array3=np.array([1],[2], [3], [4], [5])
# print(array2)

# list1=np.arange(start=1, stop=13, step=1)
# print("vector")
# print(list1)

# arr=np.array(list1.reshape(3,4))
# print("matrix")
# print(arr)

# list1=[1,2,3,4,5,6,7,8,9,10]
# arr1=np.array(list1)
# list2=[11,12,13,14,15,16,17,18,19,20]
# arr2=np.array(list2)
# add=arr1 + arr2
# print("Addition of two arrays:")
# print(add)
# sub=arr1 - arr2
# print("Subtraction of two arrays:")
# print(sub)  
# mul=arr1 * arr2
# print("Multiplication of two arrays:")
# print(mul)  
# div=arr1 / arr2
# print("Division of two arrays:")
# print(div)

# arr =array([1,2,3,4,5])
# print("Array:", arr)
# norml1 = norm(arr)
# print(norml1)

# n_array = np.array([[50, 29], [30, 44]])
# print("Matrix:")
# print(n_array)
# det = np.linalg.det(n_array)
# print("Determinant of the matrix:")
# print(int(det))

# myMatrix=np.array([[1, 2], [2, 4],[3, 6]])
# print("Matrix:")
# print(myMatrix)
# rank=np.linalg.matrix_rank(myMatrix)
# print("Rank of the matrix:",rank)




# A = np.array([[3, 7],[2, 5]])
# print("Original Matrix:")
# print(A)
# A_inv = np.linalg.inv(A)
# print("Inverse Matrix:")
# print(A_inv)






# def has_inverse(matrix):
#     det = np.linalg.det(matrix)
#     return det != 0

# m1 = np.array([[1, 2], [3, 4]])
# print("Does matrix m1 have an inverse?", has_inverse(m1))  

# m2 = np.array([[2, 4], [1, 2]])
# print("Does matrix m2 have an inverse?", has_inverse(m2)) 

# m3 = np.array([[5, 7, 9], [2, 1, 0], [3, 6, 1]])
# print("Does matrix m3 have an inverse?", has_inverse(m3))

data=pd.read_csv('data.csv')
# print(data.to_string())
# print(data.head(10))
# print(data.tail(10))
# print(data.dtypes)
# print(data.info())

test_data = pd.read_csv('test.csv')
# print(test_data.to_string())
# print(test_data.dtypes)
# test_data['rest']=test_data['rest'].str.strip('%')
# test_data['rest']=test_data['rest'].astype(float)
# assert test_data['rest'].dtype=='float'
# print(test_data['rest'].sum())
# print(test_data['valid'].describe())
#plt.hist(test_data['Rating '])
# plt.show()
#plt.title("Histogram of Valid Column")

# test_data.drop(test_data[test_data['Rating '] >5].index, inplace=False)
# print(test_data)
#plt.show()

# test_data.loc[test_data['Rating '] > 5, 'Rating '] = 5
# print(test_data)

# assert test_data['Rating '].max() <= 5 
# print(test_data)


data1 = pd.read_csv('data1.csv')
# duplicates = data1.duplicated()
# print(duplicates)

# columns_names=['Duration', 'Pulse', 'Maxpulse', 'Precentage', 'Calories']
# duplicates = data1.duplicated(subset=columns_names,keep='first')
# print(duplicates)
# data1.drop_duplicates(keep='last')
# print(data1)