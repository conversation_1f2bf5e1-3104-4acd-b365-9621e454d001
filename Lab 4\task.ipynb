# Write a python code to apply all types of threshold on an image

import cv2
import numpy as np

img = cv2.imread(r'D:\<PERSON>\NTI\ai\labs\Lab 4\MyPic.png')
gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

# Apply all types of thresholding
th1 = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
th2 = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY_INV)
th3 = cv2.threshold(gray, 127, 255, cv2.THRESH_TRUNC)
th4 = cv2.threshold(gray, 127, 255, cv2.THRESH_TOZERO)
th5 = cv2.threshold(gray, 127, 255, cv2.THRESH_TOZERO_INV)

cv2.imshow("Original", img)
cv2.imshow("Gray", gray)
cv2.imshow("THRESH_BINARY", th1)
cv2.imshow("THRESH_BINARY_INV", th2)
cv2.imshow("THRESH_TRUNC", th3)
cv2.imshow("THRESH_TOZERO", th4)
cv2.imshow("THRESH_TOZERO_INV", th5)

cv2.waitKey(0)
cv2.destroyAllWindows()

# Compare between all detectors and apply code for these algorithms

import cv2
import numpy as np

img = cv2.imread(r'D:\Ahmed Rezk\NTI\ai\labs\Lab 4\MyPic.png')
gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

# Harris Corner Detection
gray_harris = np.float32(gray)
dst = cv2.cornerHarris(gray_harris, 2, 3, 0.04)
img_harris = img.copy()
img_harris[dst > 0.01 * dst.max()] = [0, 0, 255]

# ORB Feature Detection
orb = cv2.ORB_create()
kp = orb.detect(gray, None)
img_orb = cv2.drawKeypoints(img, kp, None, color=(0, 255, 0))

cv2.imshow('Harris Corners', img_harris)
cv2.imshow('ORB Features', img_orb)
cv2.waitKey(0)
cv2.destroyAllWindows()

# Apply Harris corner detection code
import cv2
import numpy as np

img = cv2.imread(r'D:\Ahmed Rezk\NTI\ai\labs\Lab 4\MyPic.png')
gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

gray = np.float32(gray)
dst = cv2.cornerHarris(gray, 2, 3, 0.04)

img[dst > 0.01 * dst.max()] = [0, 0, 255]

cv2.imshow('Harris Corners', img)
cv2.waitKey(0)
cv2.destroyAllWindows()