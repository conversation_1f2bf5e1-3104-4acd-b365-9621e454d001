{"cells": [{"cell_type": "code", "execution_count": 11, "id": "bcd6e14c", "metadata": {}, "outputs": [], "source": ["import cv2 as cv\n", "img = cv.imread(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\MyPic.png')\n", "cv.imshow('image', img)\n", "cv.<PERSON><PERSON><PERSON>(0)\n", "cv.<PERSON><PERSON>ll<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": 12, "id": "3a1a972f", "metadata": {}, "outputs": [], "source": ["import cv2 as cv\n", "grayimg = cv.imread(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\MyPic.png', cv.IMREAD_GRAYSCALE)\n", "cv.imshow('image', grayimg)\n", "cv.<PERSON><PERSON><PERSON>(0)\n", "cv.<PERSON><PERSON>ll<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": 13, "id": "7dd19230", "metadata": {}, "outputs": [], "source": ["import cv2 as cv\n", "image_redduced_color2 = cv.imread(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\MyPic.png', cv.IMREAD_REDUCED_COLOR_2)\n", "cv.imshow('image', image_redduced_color2)\n", "cv.<PERSON><PERSON><PERSON>(0)\n", "cv.<PERSON><PERSON>ll<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": 14, "id": "b996be4e", "metadata": {}, "outputs": [], "source": ["import cv2 as cv\n", "image_redduced_grayscale8 = cv.imread(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\MyPic.png', cv.IMREAD_REDUCED_GRAYSCALE_8)\n", "cv.imshow('image', image_redduced_grayscale8)\n", "cv.<PERSON><PERSON><PERSON>(0)\n", "cv.<PERSON><PERSON>ll<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": 16, "id": "6c25d189", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["import cv2 as cv\n", "image = cv.imread(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\MyPic.png')\n", "cv.imwrite('MyPic.png', image)\n", "cv.imwrite(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\MyPic.png', image)"]}, {"cell_type": "code", "execution_count": 17, "id": "c3bbaca9", "metadata": {}, "outputs": [], "source": ["import cv2 as cv\n", "img = cv.imread(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\MyPic.png')\n", "cv.imshow('image', img)\n", "grayimg = cv.cvtColor(img, cv.COLOR_BGR2GRAY)\n", "cv.imshow('gray image', grayimg)\n", "cv.<PERSON><PERSON><PERSON>(0)\n", "cv.<PERSON><PERSON>ll<PERSON><PERSON><PERSON>()"]}, {"cell_type": "code", "execution_count": 19, "id": "99721751", "metadata": {}, "outputs": [], "source": ["import cv2 as cv\n", "img = cv.imread(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\MyPic.png')\n", "cv.imshow(\"Original Image\", img)\n", "gray_img = cv.cvtColor(img, cv.COLOR_BGR2GRAY)\n", "cv.imshow(\"Grayscale Image\", gray_img)\n", "(thresh, blackAndWhiteImg) = cv.threshold(gray_img, 127, 255, cv.THRESH_BINARY)\n", "cv.imshow(\"BlackAndWhiteImage\", blackAndWhiteImg)\n", "cv.<PERSON><PERSON><PERSON>(0)\n", "cv.<PERSON><PERSON>ll<PERSON><PERSON><PERSON>()\n"]}, {"cell_type": "code", "execution_count": 32, "id": "6bc542d2", "metadata": {}, "outputs": [], "source": ["import cv2\n", "img = cv2.imread(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\MyPic.png')\n", "B, G, R = cv2.split(img)\n", "cv2.imshow(\"OriginalImage\", img)\n", "\n", "cv2.imshow(\"Blue\", B)\n", "cv2.imshow(\"<PERSON>\", G)\n", "cv2.imshow(\"<PERSON>\", R)\n", "\n", "m = cv2.merge((B, G, R))\n", "\n", "cv2.imshow(\"Merged\", m)\n", "cv2.<PERSON><PERSON><PERSON>()\n", "cv2.destroyAllWindows()"]}, {"cell_type": "code", "execution_count": 24, "id": "a3bb9442", "metadata": {}, "outputs": [], "source": ["import cv2\n", "img = cv2.imread(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\MyPic.png')\n", "cv2.imshow(\"OriginalImage\", img)\n", "\n", "HSV_Image=cv2.cvtColor(img, cv2.COLOR_BGR2HSV)\n", "cv2.imshow(\"HSV_Image\", HSV_Image)\n", "\n", "H=HSV_Image[:,:,0]\n", "S=HSV_Image[:,:,1]\n", "V=HSV_Image[:,:,2]\n", "cv2.imshow(\"<PERSON><PERSON>\", H)\n", "cv2.imshow(\"Saturation\", S)\n", "cv2.imshow(\"Value\", V)\n", "\n", "cv2.<PERSON><PERSON><PERSON>()\n", "cv2.destroyAllWindows()"]}, {"cell_type": "code", "execution_count": 25, "id": "584c98e9", "metadata": {}, "outputs": [], "source": ["import cv2\n", "img = cv2.imread(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\MyPic.png')\n", "cv2.imshow(\"OriginalImage\", img)\n", "\n", "Half = cv2.resize(img, (0, 0), fx = 0.5, fy = 0.5)\n", "Bigger = cv2.resize(img, (1050, 1610))\n", "Stretch_near = cv2.resize(img, (780, 540), interpolation = cv2.INTER_NEAREST)\n", "\n", "cv2.imshow(\"Half\", Half)\n", "cv2.imshow(\"Big\", Bigger)\n", "cv2.imshow(\"Stretch\", Stretch_near)\n", "\n", "cv2.<PERSON><PERSON><PERSON>()\n", "cv2.destroyAllWindows()\n"]}, {"cell_type": "code", "execution_count": 38, "id": "270c0da5", "metadata": {}, "outputs": [], "source": ["import cv2\n", "img = cv2.imread(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\MyPic.png')\n", "cv2.imshow(\"OriginalImage\", img)\n", "\n", "#Average\n", "BlurImage = cv2.blur(img,(5,5))\n", "cv2.imshow('average Image', BlurImage)\n", "\n", "# <PERSON><PERSON><PERSON> Blur\n", "Gaussian = cv2.<PERSON><PERSON><PERSON><PERSON><PERSON>r(img, (7, 7), 0)\n", "cv2.imshow('Gaussian Blurring', <PERSON><PERSON><PERSON>)\n", "\n", "# Median Blur\n", "MedianImage = cv2.medianBlur(img, 5)\n", "cv2.imshow('Median Blurring', MedianImage)\n", "\n", "# Bilateral Blur\n", "BilateralImage = cv2.bilateralFilter(img, 9, 75, 75)\n", "cv2.imshow('Bilateral Blurring', BilateralImage)\n", "\n", "cv2.<PERSON><PERSON><PERSON>()\n", "cv2.destroyAllWindows()"]}, {"cell_type": "code", "execution_count": 40, "id": "30611d88", "metadata": {}, "outputs": [], "source": ["# ------ Histograms Equalization ------\n", "import cv2\n", "import numpy as np\n", "img = cv2.imread(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\MyPic.png', 0)\n", "cv2.imshow(\"image\", img)\n", "equ = cv2.equalizeHist(img)\n", "cv2.imshow(\"equ\", equ)\n", "cv2.<PERSON><PERSON><PERSON>(3000)\n", "cv2.destroyAllWindows()"]}, {"cell_type": "code", "execution_count": 48, "id": "71bda34c", "metadata": {}, "outputs": [], "source": ["import cv2\n", "\n", "img = cv2.imread(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\img.jpeg', 0)\n", "imcanny = cv2.Canny(img, 200, 200)\n", "cv2.imwrite(\"canny.jpg\", imcanny)\n", "cv2.imshow(\"original\", img)\n", "cv2.imshow(\"canny\", imcanny)\n", "cv2.<PERSON><PERSON><PERSON>()\n", "cv2.destroyAllWindows()"]}, {"cell_type": "code", "execution_count": 46, "id": "5ac17347", "metadata": {}, "outputs": [], "source": ["import cv2\n", "import numpy as np\n", "\n", "filename = r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\board.jpg'\n", "img = cv2.imread(filename)\n", "gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)\n", "cv2.imshow('original', gray)\n", "cv2.<PERSON><PERSON><PERSON>(0)\n", "cv2.destroyAllWindows()"]}, {"cell_type": "code", "execution_count": 47, "id": "f633a2ca", "metadata": {}, "outputs": [], "source": ["gray = np.float32(gray)\n", "dst = cv2.<PERSON><PERSON><PERSON><PERSON>(gray, 2, 3, 0.04)\n", "\n", "# result is dilated for marking the corners, not important\n", "dst = cv2.dilate(dst, None)\n", "\n", "# Threshold for an optimal value, it may vary depending on the image.\n", "img[dst > 0.01 * dst.max()] = [0, 0, 255]\n", "\n", "cv2.imshow('dst', img)\n", "cv2.<PERSON><PERSON><PERSON>(0)\n", "cv2.destroyAllWindows()"]}, {"cell_type": "code", "execution_count": null, "id": "3bbdfef9", "metadata": {}, "outputs": [], "source": ["import cv2\n", "\n", "# Loading the image\n", "img = cv2.imread(r'D:\\Ahmed Rezk\\NTI\\ai\\labs\\Lab 4\\pic.jpeg')\n", "\n", "# Converting image to grayscale\n", "gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)\n", "\n", "# Applying SIFT detector\n", "sift = cv2.SIFT_create()\n", "kp = sift.detect(gray, None)\n", "\n", "# Marking the keypoint on the image using circles\n", "img1 = cv2.drawKeypoints(gray,\n", "                         kp,\n", "                         img,\n", "                         flags=cv2.DRAW_MATCHES_FLAGS_DRAW_RICH_KEYPOINTS)\n", "\n", "cv2.imwrite('image-with-keypoints.jpg', img1)\n", "kp, des = sift.detectAndCompute(gray, None)\n", "\n", "cv2.imshow('Original Image', gray)\n", "cv2.imshow('image-with-keypoints.jpg', img1)\n", "\n", "cv2.<PERSON><PERSON><PERSON>()\n", "cv2.destroyAllWindows()"]}, {"cell_type": "code", "execution_count": null, "id": "594aaeae", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}